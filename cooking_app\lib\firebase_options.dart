// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBHkTJnzNWDWb-XkbKoHnnipT_VVI6CI38',
    appId: '1:780012707595:web:be62bcf33b09e5f9dc8c0e',
    messagingSenderId: '780012707595',
    projectId: 'cooking-app-10ec4',
    authDomain: 'cooking-app-10ec4.firebaseapp.com',
    storageBucket: 'cooking-app-10ec4.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDjlAR1pjUM_DjnfY5QRAQuQTHgp57mgDk',
    appId: '1:780012707595:android:f8bd708c9b520527dc8c0e',
    messagingSenderId: '780012707595',
    projectId: 'cooking-app-10ec4',
    storageBucket: 'cooking-app-10ec4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCfhKAB2ReiBVuRa5uAXRKocQgnMgAGn_w',
    appId: '1:780012707595:ios:dca6cf9efa8a5ffadc8c0e',
    messagingSenderId: '780012707595',
    projectId: 'cooking-app-10ec4',
    storageBucket: 'cooking-app-10ec4.firebasestorage.app',
    iosBundleId: 'com.example.cookingApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCfhKAB2ReiBVuRa5uAXRKocQgnMgAGn_w',
    appId: '1:780012707595:ios:dca6cf9efa8a5ffadc8c0e',
    messagingSenderId: '780012707595',
    projectId: 'cooking-app-10ec4',
    storageBucket: 'cooking-app-10ec4.firebasestorage.app',
    iosBundleId: 'com.example.cookingApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBHkTJnzNWDWb-XkbKoHnnipT_VVI6CI38',
    appId: '1:780012707595:web:ae95208b2f1bea55dc8c0e',
    messagingSenderId: '780012707595',
    projectId: 'cooking-app-10ec4',
    authDomain: 'cooking-app-10ec4.firebaseapp.com',
    storageBucket: 'cooking-app-10ec4.firebasestorage.app',
  );

}