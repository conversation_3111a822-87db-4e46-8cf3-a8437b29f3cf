// This is a basic Flutter widget test for the Cooking App.

import 'package:flutter_test/flutter_test.dart';

import 'package:cooking_app/main.dart';

void main() {
  testWidgets('App loads and shows splash screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const CookingApp());

    // Verify that the splash screen is shown
    expect(find.text('Cooking App'), findsOneWidget);
    expect(find.text('Discover amazing recipes'), findsOneWidget);
  });
}
