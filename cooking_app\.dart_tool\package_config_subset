sky_engine
3.2
file:///C:/Users/<USER>/AppData/Local/Flutter/bin/cache/pkg/sky_engine/
file:///C:/Users/<USER>/AppData/Local/Flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.3
file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter/
file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter/lib/
flutter_test
3.3
file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_test/
file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_test/lib/
flutter_web_plugins
3.2
file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_web_plugins/
file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_web_plugins/lib/
_flutterfire_internals
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.59/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.59/lib/
async
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0/lib/
boolean_selector
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
cached_network_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
characters
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0/lib/
clock
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1/lib/
cloud_firestore
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.12/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/
cloud_firestore_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/
cloud_firestore_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.12/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.12/lib/
collection
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.18.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.18.0/lib/
cross_file
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
fake_async
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1/lib/
ffi
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.3/lib/
file
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2/lib/
file_selector_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_auth
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.7.0/lib/
firebase_auth_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/
firebase_auth_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.15.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.15.3/lib/
firebase_core
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.15.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.15.2/lib/
firebase_core_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/lib/
firebase_core_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.1/lib/
firebase_storage
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage-12.4.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage-12.4.10/lib/
firebase_storage_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.10/lib/
firebase_storage_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage_web-3.10.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage_web-3.10.17/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_cache_manager
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_lints
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-4.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-4.0.0/lib/
flutter_plugin_android_lifecycle
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.26/lib/
flutter_rating_bar
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1/lib/
go_router
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-14.8.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/go_router-14.8.1/lib/
http
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2/lib/
image_picker
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+21/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+21/lib/
image_picker_for_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.19.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.19.0/lib/
leak_tracker
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.5/lib/
leak_tracker_flutter_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5/lib/
leak_tracker_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-4.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-4.0.0/lib/
logging
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.16+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.16+1/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
material_design_icons_flutter
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.15.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.15.0/lib/
mime
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0/lib/
nested
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib/
octo_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/lib/
path
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.0/lib/
path_provider
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.15/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.15/lib/
path_provider_foundation
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
platform
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/lib/
rxdart
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/lib/
shared_preferences
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.7/lib/
shared_preferences_foundation
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shimmer
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shimmer-3.0.0/lib/
source_span
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0/lib/
sprintf
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.1/lib/
sqflite_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.0/lib/
sqflite_common
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.4+6/lib/
sqflite_darwin
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/lib/
sqflite_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.1/lib/
stream_channel
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2/lib/
string_scanner
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0/lib/
synchronized
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.0+3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.0+3/lib/
term_glyph
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.2/lib/
typed_data
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib/
uuid
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.2.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.2.5/lib/
web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib/
xdg_directories
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
cooking_app
3.5
file:///C:/Users/<USER>/OneDrive/Desktop/cooking%20app/cooking_app/
file:///C:/Users/<USER>/OneDrive/Desktop/cooking%20app/cooking_app/lib/
2
